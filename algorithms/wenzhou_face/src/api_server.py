#!/usr/bin/env python3
"""
温州人脸识别算法 - FastAPI服务器
提供RESTful API接口用于人脸检测、识别和质量评估
"""

import os
import sys
import time
import json
import asyncio
import tempfile
import traceback
from pathlib import Path
from typing import List, Dict, Optional, Union
from datetime import datetime

import cv2
import numpy as np
from fastapi import FastAPI, File, UploadFile, HTTPException, Form, Query
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
import uvicorn

# 导入推理引擎
try:
    from inference_engine import WenzhouFaceEngine
except ImportError as e:
    print(f"错误：无法导入推理引擎模块: {e}")
    sys.exit(1)

# 导入日志系统
try:
    from logger_config import get_logger, get_logger_instance
    logger = get_logger()
    logger_instance = get_logger_instance()
except ImportError:
    import logging
    logger = logging.getLogger(__name__)
    logger_instance = None


# API响应模型
class APIResponse(BaseModel):
    """标准API响应格式"""
    success: bool = Field(description="请求是否成功")
    message: str = Field(description="响应消息")
    data: Optional[Dict] = Field(default=None, description="响应数据")
    error: Optional[str] = Field(default=None, description="错误信息")
    timestamp: str = Field(default_factory=lambda: datetime.now().isoformat(), description="时间戳")
    processing_time: Optional[float] = Field(default=None, description="处理时间(秒)")


class FaceDetectionResponse(BaseModel):
    """人脸检测响应"""
    num_faces: int = Field(description="检测到的人脸数量")
    faces: List[Dict] = Field(description="人脸信息列表")
    image_info: Dict = Field(description="图像信息")
    processing_time: float = Field(description="处理时间")


class FaceComparisonResponse(BaseModel):
    """人脸比对响应"""
    similarity: float = Field(description="相似度分数")
    is_same_person: bool = Field(description="是否为同一人")
    threshold: float = Field(description="判断阈值")
    confidence: str = Field(description="置信度等级")


class HealthResponse(BaseModel):
    """健康检查响应"""
    status: str = Field(description="服务状态")
    algorithm: str = Field(description="算法名称")
    version: str = Field(description="版本信息")
    uptime: float = Field(description="运行时间")
    models_loaded: bool = Field(description="模型是否已加载")


# 全局变量
app = FastAPI(
    title="温州人脸识别API",
    description="基于深度学习的人脸检测、识别和质量评估API服务",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 全局推理引擎实例
engine: Optional[WenzhouFaceEngine] = None
start_time = time.time()
config_file_path = "config.ini"  # 默认配置文件路径


async def get_engine() -> WenzhouFaceEngine:
    """获取推理引擎实例"""
    global engine
    if engine is None:
        raise HTTPException(status_code=503, detail="推理引擎未初始化")
    return engine


def validate_image_file(file: UploadFile) -> bool:
    """验证上传的图像文件"""
    # 检查文件类型
    if not file.content_type or not file.content_type.startswith('image/'):
        return False
    
    # 检查文件扩展名
    if file.filename:
        valid_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp'}
        file_ext = Path(file.filename).suffix.lower()
        return file_ext in valid_extensions
    
    return True


async def save_uploaded_file(file: UploadFile) -> str:
    """保存上传的文件到临时目录"""
    if not validate_image_file(file):
        raise HTTPException(status_code=400, detail="不支持的图像格式")
    
    # 创建临时文件
    suffix = Path(file.filename).suffix if file.filename else '.jpg'
    with tempfile.NamedTemporaryFile(delete=False, suffix=suffix) as tmp_file:
        content = await file.read()
        tmp_file.write(content)
        return tmp_file.name


def cleanup_temp_file(file_path: str):
    """清理临时文件"""
    try:
        if os.path.exists(file_path):
            os.unlink(file_path)
    except Exception as e:
        logger.warning(f"清理临时文件失败: {e}")


def get_confidence_level(similarity: float, threshold: float) -> str:
    """根据相似度获取置信度等级"""
    if similarity >= threshold + 0.2:
        return "高"
    elif similarity >= threshold + 0.1:
        return "中"
    elif similarity >= threshold:
        return "低"
    else:
        return "很低"


@app.on_event("startup")
async def startup_event():
    """应用启动事件"""
    global engine, config_file_path
    try:
        logger.info("正在初始化温州人脸识别引擎...")

        # 使用全局配置文件路径
        config_path = config_file_path
        if not os.path.exists(config_path):
            # 尝试其他可能的配置文件路径
            possible_paths = [
                "src/config/prod.ini",
                "src/config/dev.ini",
                "/app/config.ini"
            ]
            for path in possible_paths:
                if os.path.exists(path):
                    config_path = path
                    break

        logger.info(f"使用配置文件: {config_path}")
        engine = WenzhouFaceEngine(config_path)
        logger.info("温州人脸识别引擎初始化完成")
        
    except Exception as e:
        logger.error(f"引擎初始化失败: {e}")
        logger.error(traceback.format_exc())
        # 不要退出，让健康检查接口能够报告错误状态


@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭事件"""
    logger.info("温州人脸识别API服务正在关闭...")


@app.get("/", response_model=APIResponse)
async def root():
    """根路径"""
    return APIResponse(
        success=True,
        message="温州人脸识别API服务运行正常",
        data={
            "service": "wenzhou_face_api",
            "version": "1.0.0",
            "docs": "/docs",
            "health": "/api/v1/health"
        }
    )


@app.get("/api/v1/health", response_model=APIResponse)
async def health_check():
    """健康检查接口"""
    global engine, start_time
    
    uptime = time.time() - start_time
    models_loaded = engine is not None
    
    health_data = HealthResponse(
        status="healthy" if models_loaded else "unhealthy",
        algorithm="wenzhou_face",
        version="1.0.0",
        uptime=uptime,
        models_loaded=models_loaded
    )
    
    return APIResponse(
        success=models_loaded,
        message="服务状态正常" if models_loaded else "推理引擎未初始化",
        data=health_data.dict()
    )


@app.post("/api/v1/detect", response_model=APIResponse)
async def detect_faces(
    file: UploadFile = File(..., description="要检测的图像文件"),
    extract_features: bool = Query(default=True, description="是否提取人脸特征"),
    assess_quality: bool = Query(default=False, description="是否评估人脸质量"),
    max_faces: int = Query(default=100, description="最大检测人脸数量")
):
    """人脸检测接口"""
    start_time = time.time()
    temp_file_path = None
    
    try:
        # 获取推理引擎
        engine = await get_engine()
        
        # 保存上传的文件
        temp_file_path = await save_uploaded_file(file)
        
        # 执行人脸检测
        results = engine.process_image(
            temp_file_path,
            extract_features=extract_features,
            assess_quality=assess_quality
        )
        
        # 限制返回的人脸数量
        if len(results['faces']) > max_faces:
            results['faces'] = results['faces'][:max_faces]
            results['num_faces'] = len(results['faces'])
        
        # 添加图像信息
        image_info = {
            "filename": file.filename,
            "content_type": file.content_type,
            "size": file.size if hasattr(file, 'size') else None
        }
        
        processing_time = time.time() - start_time
        
        response_data = FaceDetectionResponse(
            num_faces=results['num_faces'],
            faces=results['faces'],
            image_info=image_info,
            processing_time=processing_time
        )
        
        return APIResponse(
            success=True,
            message=f"成功检测到 {results['num_faces']} 张人脸",
            data=response_data.dict(),
            processing_time=processing_time
        )
        
    except Exception as e:
        processing_time = time.time() - start_time
        error_msg = f"人脸检测失败: {str(e)}"
        logger.error(error_msg)
        logger.error(traceback.format_exc())
        
        return APIResponse(
            success=False,
            message="人脸检测失败",
            error=error_msg,
            processing_time=processing_time
        )
    
    finally:
        # 清理临时文件
        if temp_file_path:
            cleanup_temp_file(temp_file_path)


@app.post("/api/v1/compare", response_model=APIResponse)
async def compare_faces(
    file1: UploadFile = File(..., description="第一张人脸图像"),
    file2: UploadFile = File(..., description="第二张人脸图像"),
    threshold: float = Query(default=None, description="相似度阈值，默认使用配置文件中的值")
):
    """人脸比对接口"""
    start_time = time.time()
    temp_file1_path = None
    temp_file2_path = None
    
    try:
        # 获取推理引擎
        engine = await get_engine()
        
        # 保存上传的文件
        temp_file1_path = await save_uploaded_file(file1)
        temp_file2_path = await save_uploaded_file(file2)
        
        # 处理第一张图像
        results1 = engine.process_image(temp_file1_path, extract_features=True)
        if results1['num_faces'] == 0:
            raise HTTPException(status_code=400, detail="第一张图像中未检测到人脸")
        
        # 处理第二张图像
        results2 = engine.process_image(temp_file2_path, extract_features=True)
        if results2['num_faces'] == 0:
            raise HTTPException(status_code=400, detail="第二张图像中未检测到人脸")
        
        # 获取人脸特征
        feature1 = np.array(results1['faces'][0]['feature'])
        feature2 = np.array(results2['faces'][0]['feature'])
        
        # 计算相似度
        similarity = engine.compare_faces(feature1, feature2)
        
        # 获取阈值
        if threshold is None:
            threshold = engine.config.getfloat('FACE_RECOGNITION', 'similarity_threshold', fallback=0.6)
        
        # 判断结果
        is_same_person = similarity >= threshold
        confidence = get_confidence_level(similarity, threshold)
        
        processing_time = time.time() - start_time
        
        response_data = FaceComparisonResponse(
            similarity=float(similarity),
            is_same_person=is_same_person,
            threshold=threshold,
            confidence=confidence
        )
        
        return APIResponse(
            success=True,
            message=f"人脸比对完成，相似度: {similarity:.4f}",
            data=response_data.dict(),
            processing_time=processing_time
        )
        
    except HTTPException:
        raise
    except Exception as e:
        processing_time = time.time() - start_time
        error_msg = f"人脸比对失败: {str(e)}"
        logger.error(error_msg)
        logger.error(traceback.format_exc())
        
        return APIResponse(
            success=False,
            message="人脸比对失败",
            error=error_msg,
            processing_time=processing_time
        )
    
    finally:
        # 清理临时文件
        if temp_file1_path:
            cleanup_temp_file(temp_file1_path)
        if temp_file2_path:
            cleanup_temp_file(temp_file2_path)


@app.post("/api/v1/quality", response_model=APIResponse)
async def assess_face_quality(
    file: UploadFile = File(..., description="要评估质量的人脸图像"),
    min_quality_score: float = Query(default=0.5, description="最小质量分数阈值")
):
    """人脸质量评估接口"""
    start_time = time.time()
    temp_file_path = None

    try:
        # 获取推理引擎
        engine = await get_engine()

        # 检查是否启用质量评估
        if not engine.face_quality_assessor:
            raise HTTPException(
                status_code=501,
                detail="人脸质量评估功能未启用，请在配置文件中启用 enable_quality_check"
            )

        # 保存上传的文件
        temp_file_path = await save_uploaded_file(file)

        # 执行人脸检测和质量评估
        results = engine.process_image(
            temp_file_path,
            extract_features=False,
            assess_quality=True
        )

        if results['num_faces'] == 0:
            raise HTTPException(status_code=400, detail="图像中未检测到人脸")

        # 获取第一张人脸的质量信息
        face_quality = results['faces'][0].get('quality', {})
        quality_score = face_quality.get('quality_score', 0.0)

        # 判断质量是否合格
        quality_passed = quality_score >= min_quality_score

        processing_time = time.time() - start_time

        response_data = {
            "quality_assessment": face_quality,
            "quality_passed": quality_passed,
            "min_quality_threshold": min_quality_score,
            "num_faces_detected": results['num_faces'],
            "image_info": {
                "filename": file.filename,
                "content_type": file.content_type
            }
        }

        return APIResponse(
            success=True,
            message=f"人脸质量评估完成，质量分数: {quality_score:.3f}",
            data=response_data,
            processing_time=processing_time
        )

    except HTTPException:
        raise
    except Exception as e:
        processing_time = time.time() - start_time
        error_msg = f"人脸质量评估失败: {str(e)}"
        logger.error(error_msg)
        logger.error(traceback.format_exc())

        return APIResponse(
            success=False,
            message="人脸质量评估失败",
            error=error_msg,
            processing_time=processing_time
        )

    finally:
        # 清理临时文件
        if temp_file_path:
            cleanup_temp_file(temp_file_path)


@app.get("/api/v1/info", response_model=APIResponse)
async def get_algorithm_info():
    """获取算法信息"""
    try:
        engine = await get_engine()

        # 读取配置信息
        config_info = {
            "face_detection": {
                "model_path": engine.config.get('FACE_DETECTION', 'model_path', fallback='N/A'),
                "confidence_threshold": engine.config.getfloat('FACE_DETECTION', 'confidence_threshold', fallback=0.7),
                "input_size": engine.config.getint('FACE_DETECTION', 'input_size', fallback=640)
            },
            "face_recognition": {
                "model_path": engine.config.get('FACE_RECOGNITION', 'model_path', fallback='N/A'),
                "similarity_threshold": engine.config.getfloat('FACE_RECOGNITION', 'similarity_threshold', fallback=0.6),
                "input_size": engine.config.getint('FACE_RECOGNITION', 'input_size', fallback=112)
            },
            "face_quality": {
                "enabled": engine.face_quality_assessor is not None,
                "model_path": engine.config.get('FACE_QUALITY', 'model_path', fallback='N/A') if engine.face_quality_assessor else None
            }
        }

        return APIResponse(
            success=True,
            message="算法信息获取成功",
            data={
                "algorithm_name": "wenzhou_face",
                "version": "1.0.0",
                "capabilities": ["face_detection", "face_recognition", "face_quality"],
                "configuration": config_info
            }
        )

    except Exception as e:
        error_msg = f"获取算法信息失败: {str(e)}"
        logger.error(error_msg)

        return APIResponse(
            success=False,
            message="获取算法信息失败",
            error=error_msg
        )


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="温州人脸识别API服务器")
    parser.add_argument("--host", default="0.0.0.0", help="服务器主机地址")
    parser.add_argument("--port", type=int, default=8001, help="服务器端口")
    parser.add_argument("--config", default="config.ini", help="配置文件路径")
    parser.add_argument("--reload", action="store_true", help="开发模式，自动重载")

    args = parser.parse_args()

    # 设置全局配置文件路径
    config_file_path = args.config

    print(f"🚀 启动温州人脸识别API服务器")
    print(f"📍 地址: http://{args.host}:{args.port}")
    print(f"📚 API文档: http://{args.host}:{args.port}/docs")
    print(f"⚙️  配置文件: {args.config}")

    uvicorn.run(
        "api_server:app",
        host=args.host,
        port=args.port,
        reload=args.reload,
        log_level="info"
    )
