"""
算法容器管理器
负责算法容器的生命周期管理、端口分配、健康检查等核心功能
"""

import docker
import asyncio
import aiohttp
import logging
import json
import tarfile
import yaml
import os
from typing import Dict, List, Optional, Any
from datetime import datetime
from pathlib import Path

from ..models.algorithm import Algorithm, AlgorithmStatus, ContainerInfo
from .database import DatabaseManager

logger = logging.getLogger(__name__)


class AlgorithmManager:
    """算法容器管理器"""
    
    def __init__(self, data_dir: str = "/app/data"):
        self.docker_client = docker.from_env()
        self.algorithms: Dict[str, Algorithm] = {}
        self.port_counter = 8001
        self.data_dir = Path(data_dir)
        self.db = DatabaseManager()
        
        # 确保数据目录存在
        self.data_dir.mkdir(parents=True, exist_ok=True)
        (self.data_dir / "algorithms").mkdir(exist_ok=True)
        (self.data_dir / "uploads").mkdir(exist_ok=True)
        
        # 初始化时加载已有算法信息
        self._load_existing_algorithms()
    
    def _load_existing_algorithms(self):
        """加载已存在的算法信息"""
        try:
            algorithms = self.db.get_all_algorithms()
            for algo_data in algorithms:
                algorithm = Algorithm(**algo_data)
                self.algorithms[algorithm.name] = algorithm
                
                # 检查容器是否还在运行
                if algorithm.status == AlgorithmStatus.RUNNING:
                    if not self._is_container_running(algorithm.container_info.container_id):
                        algorithm.status = AlgorithmStatus.STOPPED
                        self.db.update_algorithm_status(algorithm.name, AlgorithmStatus.STOPPED)
                        
            logger.info(f"加载了 {len(self.algorithms)} 个算法")
        except Exception as e:
            logger.error(f"加载算法信息失败: {e}")
    
    def _is_container_running(self, container_id: str) -> bool:
        """检查容器是否在运行"""
        try:
            container = self.docker_client.containers.get(container_id)
            return container.status == 'running'
        except docker.errors.NotFound:
            return False
        except Exception as e:
            logger.error(f"检查容器状态失败: {e}")
            return False
    
    def _get_next_port(self) -> int:
        """获取下一个可用端口"""
        while self.port_counter < 9000:
            port = self.port_counter
            self.port_counter += 1
            
            # 检查端口是否被占用
            if not self._is_port_in_use(port):
                return port
        
        raise RuntimeError("没有可用端口")
    
    def _is_port_in_use(self, port: int) -> bool:
        """检查端口是否被占用"""
        import socket
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            return s.connect_ex(('localhost', port)) == 0
    
    async def upload_algorithm_package(self, file_content: bytes, filename: str) -> Dict[str, Any]:
        """上传并解析算法包"""
        try:
            # 保存上传的文件
            package_path = self.data_dir / "uploads" / filename
            with open(package_path, "wb") as f:
                f.write(file_content)
            
            # 解压算法包
            algorithm_name = filename.replace('.tar.gz', '').replace('.zip', '')
            extract_path = self.data_dir / "algorithms" / algorithm_name
            extract_path.mkdir(parents=True, exist_ok=True)
            
            if filename.endswith('.tar.gz'):
                with tarfile.open(package_path, 'r:gz') as tar:
                    tar.extractall(extract_path)
            else:
                raise ValueError("不支持的文件格式，请使用 .tar.gz 格式")
            
            # 读取算法元数据
            metadata_path = extract_path / "algorithm.yaml"
            if not metadata_path.exists():
                raise ValueError("算法包中缺少 algorithm.yaml 元数据文件")
            
            with open(metadata_path, 'r', encoding='utf-8') as f:
                metadata = yaml.safe_load(f)
            
            # 验证Dockerfile是否存在
            dockerfile_path = extract_path / "Dockerfile"
            if not dockerfile_path.exists():
                raise ValueError("算法包中缺少 Dockerfile")
            
            # 创建算法对象
            algorithm = Algorithm(
                name=algorithm_name,
                version=metadata.get('version', '1.0.0'),
                description=metadata.get('description', ''),
                docker_image=f"algo-{algorithm_name}:latest",
                api_port=metadata.get('api_port', 8001),
                status=AlgorithmStatus.UPLOADED,
                capabilities=metadata.get('capabilities', []),
                input_formats=metadata.get('input_formats', ['jpg', 'png']),
                package_path=str(extract_path),
                created_at=datetime.now()
            )
            
            # 保存到数据库和内存
            self.algorithms[algorithm_name] = algorithm
            self.db.save_algorithm(algorithm)
            
            logger.info(f"算法包 {algorithm_name} 上传成功")
            return {
                "status": "success",
                "algorithm": algorithm.dict(),
                "message": f"算法包 {algorithm_name} 上传成功"
            }
            
        except Exception as e:
            logger.error(f"上传算法包失败: {e}")
            raise RuntimeError(f"上传算法包失败: {str(e)}")
    
    async def build_algorithm(self, algorithm_name: str) -> Dict[str, Any]:
        """构建算法Docker镜像"""
        if algorithm_name not in self.algorithms:
            raise ValueError(f"算法 {algorithm_name} 不存在")
        
        algorithm = self.algorithms[algorithm_name]
        
        try:
            logger.info(f"开始构建算法 {algorithm_name} 的Docker镜像")
            
            # 构建Docker镜像
            image, logs = self.docker_client.images.build(
                path=algorithm.package_path,
                tag=algorithm.docker_image,
                rm=True,
                forcerm=True
            )
            
            # 更新状态
            algorithm.status = AlgorithmStatus.BUILT
            self.db.update_algorithm_status(algorithm_name, AlgorithmStatus.BUILT)
            
            logger.info(f"算法 {algorithm_name} 镜像构建成功")
            return {
                "status": "success",
                "image_id": image.id,
                "message": f"算法 {algorithm_name} 镜像构建成功"
            }
            
        except Exception as e:
            logger.error(f"构建算法镜像失败: {e}")
            algorithm.status = AlgorithmStatus.BUILD_FAILED
            self.db.update_algorithm_status(algorithm_name, AlgorithmStatus.BUILD_FAILED)
            raise RuntimeError(f"构建算法镜像失败: {str(e)}")
    
    async def start_algorithm(self, algorithm_name: str) -> Dict[str, Any]:
        """启动算法容器"""
        if algorithm_name not in self.algorithms:
            raise ValueError(f"算法 {algorithm_name} 不存在")
        
        algorithm = self.algorithms[algorithm_name]
        
        if algorithm.status == AlgorithmStatus.RUNNING:
            return {
                "status": "success",
                "message": f"算法 {algorithm_name} 已在运行",
                "port": algorithm.container_info.host_port if algorithm.container_info else None
            }
        
        try:
            # 如果镜像未构建，先构建
            if algorithm.status != AlgorithmStatus.BUILT:
                await self.build_algorithm(algorithm_name)
            
            # 分配端口
            host_port = self._get_next_port()
            
            logger.info(f"启动算法容器 {algorithm_name}，端口: {host_port}")
            
            # 启动容器
            container = self.docker_client.containers.run(
                algorithm.docker_image,
                detach=True,
                ports={f'{algorithm.api_port}/tcp': host_port},
                name=f"algo-{algorithm_name}-{host_port}",
                network="bridge",
                restart_policy={"Name": "unless-stopped"}
            )
            
            # 更新算法信息
            algorithm.container_info = ContainerInfo(
                container_id=container.id,
                container_name=container.name,
                host_port=host_port,
                api_base_url=f"http://localhost:{host_port}",
                health_check_url=f"http://localhost:{host_port}/api/v1/health"
            )
            algorithm.status = AlgorithmStatus.STARTING
            
            # 保存到数据库
            self.db.update_algorithm_container_info(algorithm_name, algorithm.container_info)
            self.db.update_algorithm_status(algorithm_name, AlgorithmStatus.STARTING)
            
            # 等待容器启动并进行健康检查
            await self._wait_for_container_ready(algorithm)
            
            logger.info(f"算法 {algorithm_name} 启动成功，端口: {host_port}")
            return {
                "status": "success",
                "port": host_port,
                "container_id": container.id,
                "api_url": algorithm.container_info.api_base_url,
                "message": f"算法 {algorithm_name} 启动成功"
            }
            
        except Exception as e:
            logger.error(f"启动算法容器失败: {e}")
            algorithm.status = AlgorithmStatus.START_FAILED
            self.db.update_algorithm_status(algorithm_name, AlgorithmStatus.START_FAILED)
            raise RuntimeError(f"启动算法容器失败: {str(e)}")
    
    async def _wait_for_container_ready(self, algorithm: Algorithm, timeout: int = 60):
        """等待容器就绪"""
        if not algorithm.container_info:
            raise RuntimeError("容器信息不存在")
        
        start_time = datetime.now()
        
        while (datetime.now() - start_time).seconds < timeout:
            try:
                async with aiohttp.ClientSession() as session:
                    async with session.get(
                        algorithm.container_info.health_check_url,
                        timeout=aiohttp.ClientTimeout(total=5)
                    ) as response:
                        if response.status == 200:
                            algorithm.status = AlgorithmStatus.RUNNING
                            self.db.update_algorithm_status(algorithm.name, AlgorithmStatus.RUNNING)
                            logger.info(f"算法 {algorithm.name} 健康检查通过")
                            return
            except Exception:
                pass
            
            await asyncio.sleep(2)
        
        # 超时，标记为启动失败
        algorithm.status = AlgorithmStatus.START_FAILED
        self.db.update_algorithm_status(algorithm.name, AlgorithmStatus.START_FAILED)
        raise RuntimeError(f"算法 {algorithm.name} 启动超时")

    async def stop_algorithm(self, algorithm_name: str) -> Dict[str, Any]:
        """停止算法容器"""
        if algorithm_name not in self.algorithms:
            raise ValueError(f"算法 {algorithm_name} 不存在")

        algorithm = self.algorithms[algorithm_name]

        if algorithm.status != AlgorithmStatus.RUNNING:
            return {
                "status": "success",
                "message": f"算法 {algorithm_name} 未在运行"
            }

        try:
            if algorithm.container_info:
                container = self.docker_client.containers.get(algorithm.container_info.container_id)
                container.stop(timeout=10)
                container.remove()

                logger.info(f"算法 {algorithm_name} 容器已停止")

            # 更新状态
            algorithm.status = AlgorithmStatus.STOPPED
            self.db.update_algorithm_status(algorithm_name, AlgorithmStatus.STOPPED)

            return {
                "status": "success",
                "message": f"算法 {algorithm_name} 已停止"
            }

        except Exception as e:
            logger.error(f"停止算法容器失败: {e}")
            raise RuntimeError(f"停止算法容器失败: {str(e)}")

    async def restart_algorithm(self, algorithm_name: str) -> Dict[str, Any]:
        """重启算法容器"""
        try:
            await self.stop_algorithm(algorithm_name)
            await asyncio.sleep(2)  # 等待容器完全停止
            return await self.start_algorithm(algorithm_name)
        except Exception as e:
            logger.error(f"重启算法容器失败: {e}")
            raise RuntimeError(f"重启算法容器失败: {str(e)}")

    async def unload_algorithm(self, algorithm_name: str) -> Dict[str, Any]:
        """卸载算法（停止容器并删除镜像）"""
        if algorithm_name not in self.algorithms:
            raise ValueError(f"算法 {algorithm_name} 不存在")

        try:
            # 先停止容器
            await self.stop_algorithm(algorithm_name)

            algorithm = self.algorithms[algorithm_name]

            # 删除Docker镜像
            try:
                self.docker_client.images.remove(algorithm.docker_image, force=True)
                logger.info(f"算法 {algorithm_name} 镜像已删除")
            except docker.errors.ImageNotFound:
                pass

            # 从内存和数据库中删除
            del self.algorithms[algorithm_name]
            self.db.delete_algorithm(algorithm_name)

            return {
                "status": "success",
                "message": f"算法 {algorithm_name} 已卸载"
            }

        except Exception as e:
            logger.error(f"卸载算法失败: {e}")
            raise RuntimeError(f"卸载算法失败: {str(e)}")

    async def get_algorithm_status(self, algorithm_name: str) -> Dict[str, Any]:
        """获取算法状态"""
        if algorithm_name not in self.algorithms:
            raise ValueError(f"算法 {algorithm_name} 不存在")

        algorithm = self.algorithms[algorithm_name]

        # 实时检查容器状态
        if algorithm.container_info and algorithm.status == AlgorithmStatus.RUNNING:
            is_running = self._is_container_running(algorithm.container_info.container_id)
            if not is_running:
                algorithm.status = AlgorithmStatus.STOPPED
                self.db.update_algorithm_status(algorithm_name, AlgorithmStatus.STOPPED)

        return {
            "name": algorithm.name,
            "status": algorithm.status.value,
            "version": algorithm.version,
            "description": algorithm.description,
            "container_info": algorithm.container_info.dict() if algorithm.container_info else None,
            "capabilities": algorithm.capabilities,
            "created_at": algorithm.created_at.isoformat() if algorithm.created_at else None
        }

    async def list_algorithms(self) -> List[Dict[str, Any]]:
        """获取所有算法列表"""
        algorithms = []
        for algorithm in self.algorithms.values():
            # 实时检查状态
            if algorithm.container_info and algorithm.status == AlgorithmStatus.RUNNING:
                is_running = self._is_container_running(algorithm.container_info.container_id)
                if not is_running:
                    algorithm.status = AlgorithmStatus.STOPPED
                    self.db.update_algorithm_status(algorithm.name, AlgorithmStatus.STOPPED)

            algorithms.append({
                "name": algorithm.name,
                "status": algorithm.status.value,
                "version": algorithm.version,
                "description": algorithm.description,
                "api_url": algorithm.container_info.api_base_url if algorithm.container_info else None,
                "capabilities": algorithm.capabilities,
                "created_at": algorithm.created_at.isoformat() if algorithm.created_at else None
            })

        return algorithms

    async def health_check_all(self) -> Dict[str, Any]:
        """对所有运行中的算法进行健康检查"""
        results = {}

        for algorithm in self.algorithms.values():
            if algorithm.status == AlgorithmStatus.RUNNING and algorithm.container_info:
                try:
                    async with aiohttp.ClientSession() as session:
                        async with session.get(
                            algorithm.container_info.health_check_url,
                            timeout=aiohttp.ClientTimeout(total=5)
                        ) as response:
                            results[algorithm.name] = {
                                "status": "healthy" if response.status == 200 else "unhealthy",
                                "response_time": response.headers.get("X-Response-Time", "unknown"),
                                "last_check": datetime.now().isoformat()
                            }
                except Exception as e:
                    results[algorithm.name] = {
                        "status": "unhealthy",
                        "error": str(e),
                        "last_check": datetime.now().isoformat()
                    }

                    # 更新状态为异常
                    algorithm.status = AlgorithmStatus.UNHEALTHY
                    self.db.update_algorithm_status(algorithm.name, AlgorithmStatus.UNHEALTHY)

        return results

    async def get_algorithm_logs(self, algorithm_name: str, lines: int = 100) -> List[str]:
        """获取算法容器日志"""
        if algorithm_name not in self.algorithms:
            raise ValueError(f"算法 {algorithm_name} 不存在")

        algorithm = self.algorithms[algorithm_name]

        if not algorithm.container_info:
            return []

        try:
            container = self.docker_client.containers.get(algorithm.container_info.container_id)
            logs = container.logs(tail=lines, timestamps=True).decode('utf-8')
            return logs.split('\n')
        except Exception as e:
            logger.error(f"获取容器日志失败: {e}")
            return [f"获取日志失败: {str(e)}"]
