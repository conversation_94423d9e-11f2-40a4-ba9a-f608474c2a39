<template>
  <div class="algorithms-page">
    <el-card class="page-header">
      <template #header>
        <div class="card-header">
          <span>🤖 算法管理</span>
          <el-button type="primary" @click="refreshAlgorithms">
            <el-icon><RefreshIcon /></el-icon>
            刷新列表
          </el-button>
        </div>
      </template>
    </el-card>

    <!-- 算法列表 -->
    <el-card>
      <template #header>
        <div class="card-header">
          <span>算法列表</span>
          <el-input
            v-model="searchText"
            placeholder="搜索算法..."
            style="width: 200px"
            clearable
          >
            <template #prefix>
              <el-icon><SearchIcon /></el-icon>
            </template>
          </el-input>
        </div>
      </template>

      <el-table
        :data="filteredAlgorithms"
        v-loading="loading"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="id" label="ID" width="200" />
        <el-table-column prop="name" label="算法名称" />
        <el-table-column prop="version" label="版本" width="100" />
        <el-table-column prop="description" label="描述" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag
              :type="getStatusType(scope.row.status)"
              size="small"
            >
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
          <template #default="scope">
            <el-button
              size="small"
              type="primary"
              @click="viewAlgorithm(scope.row)"
            >
              查看
            </el-button>
            <el-button
              size="small"
              type="success"
              @click="runAlgorithm(scope.row)"
              :disabled="scope.row.status !== 'ready'"
            >
              运行
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="algorithms.length"
          layout="total, sizes, prev, pager, next, jumper"
        />
      </div>
    </el-card>

    <!-- 算法详情对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="selectedAlgorithm?.name"
      width="600px"
    >
      <div v-if="selectedAlgorithm">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="ID">
            {{ selectedAlgorithm.id }}
          </el-descriptions-item>
          <el-descriptions-item label="版本">
            {{ selectedAlgorithm.version }}
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(selectedAlgorithm.status)">
              {{ getStatusText(selectedAlgorithm.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="环境">
            {{ selectedAlgorithm.environment }}
          </el-descriptions-item>
          <el-descriptions-item label="描述" :span="2">
            {{ selectedAlgorithm.description }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <template #footer>
        <el-button @click="dialogVisible = false">关闭</el-button>
        <el-button
          type="primary"
          @click="runAlgorithm(selectedAlgorithm)"
          :disabled="selectedAlgorithm?.status !== 'ready'"
        >
          运行算法
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Refresh as RefreshIcon,
  Search as SearchIcon
} from '@element-plus/icons-vue'

export default {
  name: 'AlgorithmsPage',
  components: {
    RefreshIcon,
    SearchIcon
  },
  setup() {
    const loading = ref(false)
    const searchText = ref('')
    const currentPage = ref(1)
    const pageSize = ref(20)
    const dialogVisible = ref(false)
    const selectedAlgorithm = ref(null)

    const algorithms = reactive([
      {
        id: 'demo-algorithm-1',
        name: '演示算法1',
        version: '1.0.0',
        status: 'ready',
        description: '这是一个演示算法，用于展示平台功能',
        environment: 'uv虚拟环境专业版'
      },
      {
        id: 'demo-algorithm-2',
        name: '图像识别算法',
        version: '2.1.0',
        status: 'running',
        description: '基于深度学习的图像识别算法',
        environment: 'uv虚拟环境专业版'
      },
      {
        id: 'demo-algorithm-3',
        name: '文本分析算法',
        version: '1.5.2',
        status: 'stopped',
        description: '自然语言处理和文本分析算法',
        environment: 'uv虚拟环境专业版'
      }
    ])

    const filteredAlgorithms = computed(() => {
      let filtered = algorithms
      
      if (searchText.value) {
        filtered = algorithms.filter(algo =>
          algo.name.toLowerCase().includes(searchText.value.toLowerCase()) ||
          algo.description.toLowerCase().includes(searchText.value.toLowerCase())
        )
      }

      const start = (currentPage.value - 1) * pageSize.value
      const end = start + pageSize.value
      return filtered.slice(start, end)
    })

    const getStatusType = (status) => {
      const statusMap = {
        ready: 'success',
        running: 'warning',
        stopped: 'info',
        error: 'danger'
      }
      return statusMap[status] || 'info'
    }

    const getStatusText = (status) => {
      const statusMap = {
        ready: '就绪',
        running: '运行中',
        stopped: '已停止',
        error: '错误'
      }
      return statusMap[status] || '未知'
    }

    const fetchAlgorithms = async () => {
      loading.value = true
      try {
        const response = await fetch('/api/algorithms/')
        if (response.ok) {
          const data = await response.json()
          if (data.success) {
            algorithms.splice(0, algorithms.length, ...data.algorithms)
          }
        }
      } catch (error) {
        console.error('获取算法列表失败:', error)
        ElMessage.error('获取算法列表失败')
      } finally {
        loading.value = false
      }
    }

    const refreshAlgorithms = async () => {
      await fetchAlgorithms()
      ElMessage.success('算法列表刷新成功')
    }

    const viewAlgorithm = (algorithm) => {
      selectedAlgorithm.value = algorithm
      dialogVisible.value = true
    }

    const runAlgorithm = async (algorithm) => {
      try {
        await ElMessageBox.confirm(
          `确定要运行算法 "${algorithm.name}" 吗？`,
          '确认运行',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )

        ElMessage.success(`算法 "${algorithm.name}" 开始运行`)
        algorithm.status = 'running'
        
        // 模拟运行完成
        setTimeout(() => {
          algorithm.status = 'ready'
          ElMessage.success(`算法 "${algorithm.name}" 运行完成`)
        }, 5000)

      } catch {
        // 用户取消
      }
    }

    onMounted(() => {
      fetchAlgorithms()
    })

    return {
      loading,
      searchText,
      currentPage,
      pageSize,
      dialogVisible,
      selectedAlgorithm,
      algorithms,
      filteredAlgorithms,
      getStatusType,
      getStatusText,
      refreshAlgorithms,
      viewAlgorithm,
      runAlgorithm
    }
  }
}
</script>

<style scoped>
.algorithms-page {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.el-card {
  margin-bottom: 20px;
}
</style>
