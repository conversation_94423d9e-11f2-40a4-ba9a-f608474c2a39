<template>
  <div id="app">
    <el-container class="layout-container">
      <!-- 顶部导航栏 -->
      <el-header class="layout-header">
        <div class="header-content">
          <div class="logo">
            <el-icon><Platform /></el-icon>
            <span>算法管理平台</span>
          </div>
          <div class="header-actions">
            <el-badge :value="runningCount" class="badge-item">
              <el-button type="primary" size="small" @click="refreshData">
                <el-icon><Refresh /></el-icon>
                刷新
              </el-button>
            </el-badge>
            <el-dropdown @command="handleCommand">
              <el-button type="info" size="small">
                系统 <el-icon><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="config">系统配置</el-dropdown-item>
                  <el-dropdown-item command="logs">系统日志</el-dropdown-item>
                  <el-dropdown-item command="about">关于</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
      </el-header>

      <el-container>
        <!-- 侧边栏 -->
        <el-aside width="250px" class="layout-aside">
          <el-menu
            :default-active="$route.path"
            router
            class="sidebar-menu"
            background-color="#304156"
            text-color="#bfcbd9"
            active-text-color="#409EFF"
          >
            <el-menu-item index="/">
              <el-icon><Monitor /></el-icon>
              <span>仪表盘</span>
            </el-menu-item>
            <el-menu-item index="/algorithms">
              <el-icon><DataAnalysis /></el-icon>
              <span>算法管理</span>
            </el-menu-item>
            <el-menu-item index="/tasks">
              <el-icon><List /></el-icon>
              <span>任务历史</span>
            </el-menu-item>
            <el-menu-item index="/system">
              <el-icon><Setting /></el-icon>
              <span>系统配置</span>
            </el-menu-item>
          </el-menu>
        </el-aside>

        <!-- 主内容区 -->
        <el-main class="layout-main">
          <router-view />
        </el-main>
      </el-container>
    </el-container>

    <!-- 系统配置对话框 -->
    <SystemConfigDialog v-model="showConfigDialog" @updated="refreshData" />
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessageBox } from 'element-plus'
import SystemConfigDialog from '@/components/SystemConfigDialog.vue'

const router = useRouter()
const showConfigDialog = ref(false)
const runningCount = ref(1) // 简化版本，直接设置数值

// 刷新数据
const refreshData = async () => {
  try {
    const response = await fetch('/api/system/stats')
    if (response.ok) {
      const data = await response.json()
      if (data.success) {
        runningCount.value = data.stats.running_algorithms || 0
      }
    }
  } catch (error) {
    console.error('刷新数据失败:', error)
  }
}

// 处理下拉菜单命令
const handleCommand = (command) => {
  switch (command) {
    case 'config':
      showConfigDialog.value = true
      break
    case 'logs':
      router.push('/system')
      break
    case 'about':
      ElMessageBox.alert(
        '算法管理平台专业版 v1.0.0\n统一管理和调用AI算法容器的专业平台\n使用uv虚拟环境 + Vue 3 + Vite',
        '关于',
        { confirmButtonText: '确定' }
      )
      break
  }
}

// 组件挂载时获取数据
onMounted(() => {
  refreshData()
})
</script>

<style scoped>
.layout-container {
  height: 100vh;
}

.layout-header {
  background-color: #fff;
  border-bottom: 1px solid #e6e6e6;
  padding: 0;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
  padding: 0 20px;
}

.logo {
  display: flex;
  align-items: center;
  font-size: 20px;
  font-weight: bold;
  color: #409EFF;
}

.logo .el-icon {
  margin-right: 8px;
  font-size: 24px;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.badge-item {
  margin-right: 12px;
}

.layout-aside {
  background-color: #304156;
}

.sidebar-menu {
  border-right: none;
  height: 100%;
  background-color: #304156;
}

.sidebar-menu .el-menu-item {
  color: #bfcbd9;
}

.sidebar-menu .el-menu-item:hover {
  background-color: #263445;
  color: #409EFF;
}

.sidebar-menu .el-menu-item.is-active {
  background-color: #409EFF;
  color: #fff;
}

.layout-main {
  background-color: #f0f2f5;
  padding: 20px;
}
</style>

<style>
/* 全局样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

#app {
  height: 100vh;
}

/* Element Plus 样式覆盖 */
.el-menu-item.is-active {
  background-color: #409EFF !important;
  color: #fff !important;
}

.el-menu-item {
  color: #bfcbd9 !important;
}

.el-menu-item:hover {
  background-color: #263445 !important;
  color: #409EFF !important;
}

.el-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.el-button {
  border-radius: 6px;
}

.el-table {
  border-radius: 8px;
  overflow: hidden;
}
</style>
